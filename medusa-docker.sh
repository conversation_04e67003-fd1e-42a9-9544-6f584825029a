#!/bin/bash

# Medusa Docker Installation Script
# This script automates the complete setup of Medusa using Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Docker installation
check_docker() {
    print_status "Checking Docker installation..."

    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        print_status "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker."
        exit 1
    fi

    print_success "Docker is installed and running"
}

# Function to check if we're in the correct directory
check_project_directory() {
    print_status "Checking project directory..."

    if [[ ! -f "package.json" ]] || [[ ! -f "docker-compose.yml" ]] || [[ ! -f "Dockerfile" ]]; then
        print_error "This doesn't appear to be a Medusa project directory."
        print_error "Please run this script from the root of your Medusa project."
        exit 1
    fi

    print_success "Found Medusa project files"
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."

    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.template" ]]; then
            print_status "Creating .env file from template..."
            cp .env.template .env
            print_warning "Please review and update the .env file with your specific configuration"
        else
            print_status "Creating default .env file..."
            cat > .env << EOF
STORE_CORS=http://localhost:8000,https://docs.medusajs.com
ADMIN_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
AUTH_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
REDIS_URL=redis://redis:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
DATABASE_URL=*******************************************/medusa-store
DB_NAME=medusa-store
POSTGRES_URL=*******************************************/medusa-store
EOF
        fi
    else
        print_success "Environment file already exists"
        # Update Redis and Database URLs to use Docker service names
        sed -i.bak 's|redis://localhost:6379|redis://redis:6379|g' .env
        sed -i.bak 's|@localhost:5432|@postgres:5432|g' .env
        sed -i.bak 's|@localhost:5433|@postgres:5432|g' .env
        print_status "Updated .env file for Docker networking"
    fi
}

# Function to clean up existing containers
cleanup_containers() {
    print_status "Cleaning up existing containers..."

    # Stop and remove existing containers
    docker-compose down --remove-orphans 2>/dev/null || docker compose down --remove-orphans 2>/dev/null || true

    # Remove any dangling images
    docker image prune -f >/dev/null 2>&1 || true

    print_success "Cleaned up existing containers"
}

# Function to build and start services
start_services() {
    print_status "Building and starting Medusa services..."

    # Use docker-compose or docker compose based on availability
    if command_exists docker-compose; then
        COMPOSE_CMD="docker-compose"
    else
        COMPOSE_CMD="docker compose"
    fi

    print_status "Building Docker images..."
    $COMPOSE_CMD build --no-cache

    print_status "Starting services in detached mode..."
    $COMPOSE_CMD up -d

    print_success "Services started successfully"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."

    # Wait for PostgreSQL
    print_status "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker exec medusa_postgres pg_isready -U postgres >/dev/null 2>&1; then
            print_success "PostgreSQL is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "PostgreSQL failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done

    # Wait for Redis
    print_status "Waiting for Redis to be ready..."
    for i in {1..30}; do
        if docker exec medusa_redis redis-cli ping >/dev/null 2>&1; then
            print_success "Redis is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Redis failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done
}

# Function to run database migrations and seeding
setup_database() {
    print_status "Setting up database..."

    # Wait a bit more for the Medusa container to be fully ready
    sleep 10

    print_status "Running database migrations..."
    docker exec medusa_backend npx medusa db:migrate || {
        print_error "Database migration failed"
        print_status "Checking container logs..."
        docker logs medusa_backend --tail 50
        exit 1
    }

    print_status "Seeding database with initial data..."
    docker exec medusa_backend npm run seed || {
        print_warning "Database seeding failed, but continuing..."
    }

    print_success "Database setup completed"
}

# Function to check if services are running
check_services() {
    print_status "Checking service health..."

    # Check if containers are running
    if ! docker ps | grep -q "medusa_backend"; then
        print_error "Medusa backend container is not running"
        return 1
    fi

    if ! docker ps | grep -q "medusa_postgres"; then
        print_error "PostgreSQL container is not running"
        return 1
    fi

    if ! docker ps | grep -q "medusa_redis"; then
        print_error "Redis container is not running"
        return 1
    fi

    # Test API endpoint
    print_status "Testing API endpoint..."
    for i in {1..10}; do
        if curl -s http://localhost:9000/store/products >/dev/null 2>&1; then
            print_success "Medusa API is responding"
            return 0
        fi
        sleep 3
    done

    print_warning "API endpoint test failed, but services appear to be running"
    return 0
}

# Function to display final information
display_info() {
    print_success "Medusa installation completed successfully!"
    echo
    echo "🚀 Your Medusa store is now running!"
    echo
    echo "📍 Service URLs:"
    echo "   • Medusa API: http://localhost:9000"
    echo "   • Admin Dashboard: http://localhost:9000/app"
    echo "   • PostgreSQL: localhost:5432"
    echo "   • Redis: localhost:6379"
    echo
    echo "🔧 Useful commands:"
    echo "   • View logs: docker-compose logs -f"
    echo "   • Stop services: docker-compose down"
    echo "   • Restart services: docker-compose restart"
    echo "   • Access backend shell: docker exec -it medusa_backend sh"
    echo
    echo "📚 Next steps:"
    echo "   1. Visit http://localhost:9000/app to access the admin dashboard"
    echo "   2. Create your admin user account"
    echo "   3. Start building your store!"
    echo
}

# Main execution
main() {
    echo "🐳 Medusa Docker Installation Script"
    echo "===================================="
    echo

    check_docker
    check_project_directory
    setup_environment
    cleanup_containers
    start_services
    wait_for_services
    setup_database

    if check_services; then
        display_info
    else
        print_error "Some services may not be working correctly"
        print_status "Check logs with: docker-compose logs"
        exit 1
    fi
}

# Handle script interruption
trap 'print_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"