#!/bin/bash

# Medusa Docker Installation Script
# This script automates the complete setup of Medusa using Docker
# It follows the official Medusa installation guide step by step

set -e  # Exit on any error

# Configuration
PROJECT_NAME="my-medusa-store"
MEDUSA_VERSION="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        print_status "Visit: https://nodejs.org/"
        exit 1
    fi

    # Check Node.js version
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
        exit 1
    fi

    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi

    # Check Docker
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        print_status "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker."
        exit 1
    fi

    print_success "All prerequisites are installed"
}

# Function to create new Medusa project
create_medusa_project() {
    print_status "Creating new Medusa project..."

    # Check if project directory already exists
    if [[ -d "$PROJECT_NAME" ]]; then
        print_warning "Project directory '$PROJECT_NAME' already exists"
        read -p "Do you want to remove it and create a new one? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Removing existing project directory..."
            rm -rf "$PROJECT_NAME"
        else
            print_status "Using existing project directory..."
            cd "$PROJECT_NAME"
            return 0
        fi
    fi

    # Create new Medusa project using npx
    print_status "Running: npx create-medusa-app@latest $PROJECT_NAME"
    npx create-medusa-app@latest "$PROJECT_NAME" --skip-db --skip-env --skip-migrations

    cd "$PROJECT_NAME"
    print_success "Medusa project created successfully"
}

# Function to create Docker configuration files
create_docker_files() {
    print_status "Creating Docker configuration files..."

    # Create Dockerfile
    print_status "Creating Dockerfile..."
    cat > Dockerfile << 'EOF'
# Development Dockerfile for Medusa
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Create start script
RUN echo '#!/bin/sh' > start.sh && \
    echo 'echo "Running database migrations..."' >> start.sh && \
    echo 'npx medusa db:migrate' >> start.sh && \
    echo 'echo "Seeding database..."' >> start.sh && \
    echo 'npm run seed || echo "Seeding failed, continuing..."' >> start.sh && \
    echo 'echo "Starting Medusa development server..."' >> start.sh && \
    echo 'npm run dev' >> start.sh && \
    chmod +x start.sh

# Expose the port Medusa runs on
EXPOSE 9000

# Start with migrations and then the development server
CMD ["./start.sh"]
EOF

    # Create docker-compose.yml
    print_status "Creating docker-compose.yml..."
    cat > docker-compose.yml << 'EOF'
services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medusa_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: medusa-store
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: medusa123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - medusa_network

  # Redis
  redis:
    image: redis:7-alpine
    container_name: medusa_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - medusa_network

  # Medusa Server
  medusa:
    build: .
    container_name: medusa_backend
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    ports:
      - "9000:9000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*******************************************/medusa-store
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - medusa_network

volumes:
  postgres_data:

networks:
  medusa_network:
    driver: bridge
EOF

    # Create .dockerignore
    print_status "Creating .dockerignore..."
    cat > .dockerignore << 'EOF'
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.git
.gitignore
README.md
.env.test
.nyc_output
coverage
.DS_Store
*.log
dist
build
Dockerfile
docker-compose.yml
.dockerignore
EOF

    print_success "Docker configuration files created"
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."

    # Create .env file with Docker-compatible settings
    print_status "Creating .env file..."
    cat > .env << 'EOF'
STORE_CORS=http://localhost:8000,https://docs.medusajs.com
ADMIN_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
AUTH_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
REDIS_URL=redis://redis:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
DATABASE_URL=*******************************************/medusa-store
DB_NAME=medusa-store
POSTGRES_URL=*******************************************/medusa-store
EOF

    print_success "Environment file created"
}

# Function to clean up existing containers
cleanup_containers() {
    print_status "Cleaning up existing containers..."

    # Stop and remove existing containers
    docker-compose down --remove-orphans 2>/dev/null || docker compose down --remove-orphans 2>/dev/null || true

    # Remove any dangling images
    docker image prune -f >/dev/null 2>&1 || true

    print_success "Cleaned up existing containers"
}

# Function to build and start services
start_services() {
    print_status "Building and starting Medusa services..."

    # Use docker-compose or docker compose based on availability
    if command_exists docker-compose; then
        COMPOSE_CMD="docker-compose"
    else
        COMPOSE_CMD="docker compose"
    fi

    print_status "Building Docker images..."
    $COMPOSE_CMD build --no-cache

    print_status "Starting services in detached mode..."
    $COMPOSE_CMD up -d

    print_success "Services started successfully"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."

    # Wait for PostgreSQL
    print_status "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker exec medusa_postgres pg_isready -U postgres >/dev/null 2>&1; then
            print_success "PostgreSQL is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "PostgreSQL failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done

    # Wait for Redis
    print_status "Waiting for Redis to be ready..."
    for i in {1..30}; do
        if docker exec medusa_redis redis-cli ping >/dev/null 2>&1; then
            print_success "Redis is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Redis failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done
}

# Function to run database migrations and seeding
setup_database() {
    print_status "Setting up database..."

    # Wait for the Medusa container to be fully ready
    print_status "Waiting for Medusa backend to be ready..."
    sleep 15

    # Check if container is running
    if ! docker ps | grep -q "medusa_backend"; then
        print_error "Medusa backend container is not running"
        print_status "Container logs:"
        docker logs medusa_backend --tail 20
        exit 1
    fi

    print_status "Running database migrations..."
    docker exec medusa_backend npx medusa db:migrate || {
        print_error "Database migration failed"
        print_status "Checking container logs..."
        docker logs medusa_backend --tail 50
        exit 1
    }

    print_status "Seeding database with initial data..."
    docker exec medusa_backend npm run seed || {
        print_warning "Database seeding failed, but continuing..."
        print_status "This is normal for a fresh installation"
    }

    print_success "Database setup completed"
}

# Function to check if services are running
check_services() {
    print_status "Checking service health..."

    # Check if containers are running
    if ! docker ps | grep -q "medusa_backend"; then
        print_error "Medusa backend container is not running"
        return 1
    fi

    if ! docker ps | grep -q "medusa_postgres"; then
        print_error "PostgreSQL container is not running"
        return 1
    fi

    if ! docker ps | grep -q "medusa_redis"; then
        print_error "Redis container is not running"
        return 1
    fi

    # Test API endpoint
    print_status "Testing API endpoint..."
    for i in {1..10}; do
        if curl -s http://localhost:9000/store/products >/dev/null 2>&1; then
            print_success "Medusa API is responding"
            return 0
        fi
        sleep 3
    done

    print_warning "API endpoint test failed, but services appear to be running"
    return 0
}

# Function to display final information
display_info() {
    print_success "Medusa installation completed successfully!"
    echo
    echo "🚀 Your Medusa store is now running!"
    echo
    echo "� Project directory: $(pwd)"
    echo
    echo "�📍 Service URLs:"
    echo "   • Medusa API: http://localhost:9000"
    echo "   • Admin Dashboard: http://localhost:9000/app"
    echo "   • PostgreSQL: localhost:5432 (user: postgres, password: medusa123)"
    echo "   • Redis: localhost:6379"
    echo
    echo "🔧 Useful commands (run from project directory):"
    echo "   • View logs: docker-compose logs -f"
    echo "   • Stop services: docker-compose down"
    echo "   • Restart services: docker-compose restart"
    echo "   • Access backend shell: docker exec -it medusa_backend sh"
    echo "   • View specific service logs: docker-compose logs -f medusa"
    echo
    echo "📚 Next steps:"
    echo "   1. Visit http://localhost:9000/app to access the admin dashboard"
    echo "   2. Create your admin user account"
    echo "   3. Start building your store!"
    echo "   4. Check the documentation: https://docs.medusajs.com"
    echo
    echo "💡 Troubleshooting:"
    echo "   • If services don't start: docker-compose down && docker-compose up --build"
    echo "   • If database issues: docker-compose down -v && docker-compose up --build"
    echo
}

# Main execution
main() {
    echo "🐳 Medusa Docker Installation Script"
    echo "===================================="
    echo "This script will create a new Medusa project and set it up with Docker"
    echo

    # Step 1: Check prerequisites
    check_prerequisites

    # Step 2: Create Medusa project (or use existing)
    create_medusa_project

    # Step 3: Create Docker configuration files
    create_docker_files

    # Step 4: Setup environment
    setup_environment

    # Step 5: Clean up any existing containers
    cleanup_containers

    # Step 6: Build and start services
    start_services

    # Step 7: Wait for services to be ready
    wait_for_services

    # Step 8: Setup database
    setup_database

    # Step 9: Final health check and display info
    if check_services; then
        display_info
    else
        print_error "Some services may not be working correctly"
        print_status "Check logs with: docker-compose logs"
        exit 1
    fi
}

# Handle script interruption
trap 'print_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"